_target_: nuplan.planning.scenario_builder.scenario_filter.ScenarioFilter
_convert_: "all"

scenario_types: null

scenario_tokens:
  - "2dedd41ee75d52ff"
  - "6be690074b835ee7"
  - "8d1cc9933b715e6c"
  - "c1181b16da455fb9"

log_names: null
map_names: null

num_scenarios_per_type: null
limit_total_scenarios: null
timestamp_threshold_s: null
ego_displacement_minimum_m: null # Whether to remove scenarios where the ego moves less than a certain amount
ego_start_speed_threshold: null # Limit to scenarios where the ego reaches a certain speed from below
ego_stop_speed_threshold: null # Limit to scenarios where the ego reaches a certain speed from above
speed_noise_tolerance: null # Value at or below which a speed change between two timepoints should be ignored as noise.

expand_scenarios: false
remove_invalid_goals: false
shuffle: false
