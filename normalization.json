{"ego": {"mean": [10, 0, 0, 0], "std": [20, 20, 1, 1]}, "neighbor": {"mean": [10, 0, 0, 0], "std": [20, 20, 1, 1]}, "ego_current_state": {"mean": [10, 0, 0, 0, 0, 0, 0, 0, 0, 0], "std": [20, 20, 1, 1, 20, 20, 20, 20, 1, 1]}, "neighbor_agents_past": {"mean": [10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "std": [20, 20, 1, 1, 20, 20, 20, 20, 1, 1, 1]}, "static_objects": {"mean": [10, 0, 0, 0, 0, 0, 0, 0, 0, 0], "std": [20, 20, 1, 1, 20, 20, 1, 1, 1, 1]}, "lanes": {"mean": [10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "std": [20, 20, 20, 20, 20, 20, 20, 20, 1, 1, 1, 1]}, "lanes_speed_limit": {"mean": [0], "std": [20]}, "route_lanes": {"mean": [10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "std": [20, 20, 20, 20, 20, 20, 20, 20, 1, 1, 1, 1]}, "route_lanes_speed_limit": {"mean": [0], "std": [20]}}