diffusion_planner:
  _target_: diffusion_planner.planner.planner.DiffusionPlanner
  _convert_: "all"

  config:
    _target_: diffusion_planner.utils.config.Config
    _convert_: "all"

    args_file: ???

    guidance_fn:
      _target_: diffusion_planner.model.guidance.guidance_wrapper.GuidanceWrapper
      _convert_: "all"

  ckpt_path: ???

  past_trajectory_sampling:
    _target_: nuplan.planning.simulation.trajectory.trajectory_sampling.TrajectorySampling
    _convert_: "all"

    num_poses: 20
    time_horizon: 2

  future_trajectory_sampling:
    _target_: nuplan.planning.simulation.trajectory.trajectory_sampling.TrajectorySampling
    _convert_: "all"

    num_poses: 80
    time_horizon: 8

  device: cuda
