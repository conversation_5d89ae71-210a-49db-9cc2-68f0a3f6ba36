{"cells": [{"cell_type": "code", "execution_count": null, "id": "f2fe1959-9fad-48c8-ae0f-00d6af375a83", "metadata": {}, "outputs": [], "source": ["# Useful imports\n", "import os\n", "from pathlib import Path\n", "import tempfile\n", "import hydra\n", "import sys"]}, {"cell_type": "markdown", "id": "951b38bc", "metadata": {}, "source": ["### User Configuration Section"]}, {"cell_type": "code", "execution_count": null, "id": "acac0e11", "metadata": {}, "outputs": [], "source": ["RESULT_FOLDER = \"REPLACE_WITH_RESULT_PATH\" # simulation result absolute path (e.g., \"/data/nuplan-v1.1/exp/exp/simulation/closed_loop_nonreactive_agents/diffusion_planner/val14/diffusion_planner_release/model_2025-01-25-18-29-09\")\n", "env_variables = {\n", "    \"NUPLAN_DEVKIT_ROOT\": \"REPLACE_WITH_NUPLAN_DEVIKIT_DIR\",  # nuplan-devkit absolute path (e.g., \"/home/<USER>/nuplan-devkit\")\n", "    \"NUPLAN_DATA_ROOT\": \"REPLACE_WITH_DATA_DIR\", # nuplan dataset absolute path (e.g. \"/data\")\n", "    \"NUPLAN_MAPS_ROOT\": \"REPLACE_WITH_MAPS_DIR\", # nuplan maps absolute path (e.g. \"/data/nuplan-v1.1/maps\")\n", "    \"NUPLAN_EXP_ROOT\": \"REPLACE_WITH_EXP_DIR\", # nuplan experiment absolute path (e.g. \"/data/nuplan-v1.1/exp\")\n", "    \"NUPLAN_SIMULATION_ALLOW_ANY_BUILDER\":\"1\"\n", "}\n", "\n", "for k, v in env_variables.items():\n", "    os.environ[k] = v\n", "\n", "# Location of path with all nuBoard configs\n", "CONFIG_PATH = '../nuplan-devkit/nuplan/planning/script/config/nuboard' # relative path to nuplan-devkit"]}, {"cell_type": "code", "execution_count": null, "id": "6bbaf495-5298-4f99-8f2a-6cdf9e9a61a3", "metadata": {}, "outputs": [], "source": ["CONFIG_NAME = 'default_nuboard'\n", "\n", "# Initialize configuration management system\n", "hydra.core.global_hydra.GlobalHydra.instance().clear()  # reinitialize hydra if already initialized\n", "hydra.initialize(config_path=CONFIG_PATH)\n", "\n", "ml_planner_simulation_folder = RESULT_FOLDER\n", "ml_planner_simulation_folder = [dp for dp, _, fn in os.walk(ml_planner_simulation_folder) if True in ['.nuboard' in x for x in fn]]\n", "\n", "# Compose the configuration\n", "cfg = hydra.compose(config_name=CONFIG_NAME, overrides=[\n", "    'scenario_builder=nuplan',  # set the database (same as simulation) used to fetch data for visualization\n", "    f'simulation_path={ml_planner_simulation_folder}',  # nuboard file path(s), if left empty the user can open the file inside nuBoard\n", "    'hydra.searchpath=[pkg://diffusion_planner.config.scenario_filter, pkg://diffusion_planner.config, pkg://nuplan.planning.script.config.common, pkg://nuplan.planning.script.experiments]',\n", "    'port_number=6599'\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "0f416202-cd79-4251-8fe7-a06d6770ed7c", "metadata": {}, "outputs": [], "source": ["from nuplan.planning.script.run_nuboard import main as main_nuboard\n", "\n", "# Run nuBoard\n", "main_nuboard(cfg)"]}], "metadata": {"kernelspec": {"display_name": "nuplan", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 5}