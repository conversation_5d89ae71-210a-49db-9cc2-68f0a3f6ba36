import numpy as np
from tqdm import tqdm

from nuplan.common.actor_state.state_representation import Point2D

from diffusion_planner.data_process.roadblock_utils import route_roadblock_correction
from diffusion_planner.data_process.agent_process import (
agent_past_process, 
sampled_tracked_objects_to_array_list,
sampled_static_objects_to_array_list,
agent_future_process
)
from diffusion_planner.data_process.map_process import get_neighbor_vector_set_map, map_process
from diffusion_planner.data_process.ego_process import get_ego_past_array_from_scenario, get_ego_future_array_from_scenario, calculate_additional_ego_states
from diffusion_planner.data_process.utils import convert_to_model_inputs

class DataProcessor(object):
    def process_scenario(scenario, num_past_poses, past_time_horizon, num_agents, num_static,
    max_ped_bike, map_features, radius, max_elements, max_points,
    num_future_poses, future_time_horizon, save_dir):
        map_name = scenario._map_name
        token = scenario.token
        map_api = scenario.map_api  


        ego_state = scenario.initial_ego_state
        ego_coords = Point2D(ego_state.rear_axle.x, ego_state.rear_axle.y)
        anchor_ego_state = np.array([ego_state.rear_axle.x, ego_state.rear_axle.y, ego_state.rear_axle.heading], dtype=np.float64)
        ego_agent_past, time_stamps_past = get_ego_past_array_from_scenario(scenario, num_past_poses, past_time_horizon)

        present_tracked_objects = scenario.initial_tracked_objects.tracked_objects
        past_tracked_objects = [
            tracked_objects.tracked_objects
            for tracked_objects in scenario.get_past_tracked_objects(
                iteration=0, time_horizon=past_time_horizon, num_samples=num_past_poses
            )
        ]
        sampled_past_observations = past_tracked_objects + [present_tracked_objects]
        neighbor_agents_past, neighbor_agents_types = \
            sampled_tracked_objects_to_array_list(sampled_past_observations)

        static_objects, static_objects_types = sampled_static_objects_to_array_list(present_tracked_objects)

        ego_agent_past, neighbor_agents_past, neighbor_indices, static_objects = \
            agent_past_process(ego_agent_past, neighbor_agents_past, neighbor_agents_types, num_agents, static_objects, static_objects_types, num_static, max_ped_bike, anchor_ego_state)


        route_roadblock_ids = scenario.get_route_roadblock_ids()
        traffic_light_data = list(scenario.get_traffic_light_status_at_iteration(0))

        if route_roadblock_ids != ['']:
            route_roadblock_ids = route_roadblock_correction(
                ego_state, map_api, route_roadblock_ids
            )

        coords, traffic_light_data, speed_limit, lane_route = get_neighbor_vector_set_map(
            map_api, map_features, ego_coords, radius, traffic_light_data
        )

        vector_map = map_process(route_roadblock_ids, anchor_ego_state, coords, traffic_light_data, speed_limit, lane_route, map_features, 
                                max_elements, max_points)


        ego_agent_future = get_ego_future_array_from_scenario(scenario, ego_state, num_future_poses, future_time_horizon)

        present_tracked_objects = scenario.initial_tracked_objects.tracked_objects
        future_tracked_objects = [
            tracked_objects.tracked_objects
            for tracked_objects in scenario.get_future_tracked_objects(
                iteration=0, time_horizon=future_time_horizon, num_samples=num_future_poses
            )
        ]

        sampled_future_observations = [present_tracked_objects] + future_tracked_objects
        future_tracked_objects_array_list, _ = sampled_tracked_objects_to_array_list(sampled_future_observations)
        neighbor_agents_future = agent_future_process(anchor_ego_state, future_tracked_objects_array_list, num_agents, neighbor_indices)



        ego_current_state = calculate_additional_ego_states(ego_agent_past, time_stamps_past)

        # gather data
        data = {"map_name": map_name, "token": token, "ego_current_state": ego_current_state, "ego_agent_future": ego_agent_future,
                "neighbor_agents_past": neighbor_agents_past, "neighbor_agents_future": neighbor_agents_future, "static_objects": static_objects}
        data.update(vector_map)

        np.savez(f"{save_dir}/{data['map_name']}_{data['token']}.npz", **data)


def work(self, scenarios: list[NuPlanScenario]):
        # Create a pool of workers
    num_processes = multiprocessing.cpu_count()  # or set a fixed number if preferred
    pool = multiprocessing.Pool(processes=num_processes)
    
    # Prepare arguments for each scenario
    args = [(scenario, self.num_past_poses, self.past_time_horizon, self.num_agents, 
            self.num_static, self.max_ped_bike, self._map_features, self._radius, 
            self._max_elements, self._max_points, self.num_future_poses, 
            self.future_time_horizon, self._save_dir) for scenario in scenarios]
    
    # Process scenarios in parallel with progress bar
    for _ in tqdm(pool.starmap(process_scenario, args), total=len(scenarios)):
        pass
    
    # Clean up
    pool.close()
    pool.join()