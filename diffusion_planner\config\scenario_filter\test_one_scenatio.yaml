_target_: nuplan.planning.scenario_builder.scenario_filter.ScenarioFilter
_convert_: "all"

scenario_types: null 

scenario_tokens:
  - "4eb6d1ceffdc5ef1"

log_names: ${splitter.log_splits.val}
map_names: null

num_scenarios_per_type: 1
limit_total_scenarios: null
timestamp_threshold_s: 15
ego_displacement_minimum_m: null # Whether to remove scenarios where the ego moves less than a certain amount
ego_start_speed_threshold: null # Limit to scenarios where the ego reaches a certain speed from below
ego_stop_speed_threshold: null # Limit to scenarios where the ego reaches a certain speed from above
speed_noise_tolerance: null # Value at or below which a speed change between two timepoints should be ignored as noise.

expand_scenarios: false
remove_invalid_goals: true
shuffle: false